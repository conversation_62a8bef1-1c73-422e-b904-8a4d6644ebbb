/* eslint-disable @typescript-eslint/no-unsafe-call */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import helmet from 'helmet';
import compression from 'compression';
import { ResponseInterceptor } from './common/interceptors/resposne.interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    rawBody: true,
  });

  app.enableCors();
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
    }),
  );

  if (process.env.NODE_ENV === 'production')
    app.useGlobalInterceptors(new ResponseInterceptor());

  app.setGlobalPrefix('api/holdx/');
  app.enableShutdownHooks();

  app.enableVersioning();

  app.use(helmet());
  app.use(compression());

  // app.useGlobalFilters(new HttpExceptionFilter());

  const config = new DocumentBuilder()
    .setTitle('Hold X')
    .setDescription('Hold X is an escrow platform for Cameroon')
    .setVersion('1.0')
    .addBearerAuth()
    .setContact('Hold X', 'https://holdx.com', '<EMAIL>')
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/holdx/docs', app, documentFactory);

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
