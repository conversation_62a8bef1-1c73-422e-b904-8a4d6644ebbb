import { ConfigService } from '@nestjs/config';
import { MailerModule } from '@nestjs-modules/mailer';
import { Global, Module } from '@nestjs/common';
import { EmailsService } from './emails.service';

@Global()
@Module({
  imports: [
    MailerModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        transport: {
          host: configService.get('EMAIL_HOST') as string,
          port: configService.get('EMAIL_PORT') as number,
          secure: false,
          auth: {
            user: configService.get('EMAIL_USER') as string,
            pass: configService.get('EMAIL_PASS') as string,
          },
          tls: {
            rejectUnauthorized: false,
          },
          debug: true,
        },
        defaults: {
          from: '"No Reply" <<EMAIL>>',
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [EmailsService],
  exports: [EmailsService],
})
export class EmailsModule {}
