import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class EmailsService {
  private readonly logger = new Logger(EmailsService.name);

  constructor(private readonly mailerService: MailerService) {}

  async sendWelcomeEmail(userEmail: string, userName: string): Promise<void> {
    try {
      const res = await this.mailerService.sendMail({
        to: userEmail,
        subject: 'Welcome to Hold X!',
        // template: 'welcome.template.ejs',
        // context: {
        //   userName,
        //   platformName: 'Hold X',
        //   loginUrl: 'https://holdx.com/login',
        //   unsubscribeUrl: 'https://holdx.com/unsubscribe',
        //   platformDomain: 'holdx.com',
        // },
      });
      console.log(res);
    } catch (error) {
      this.logger.error(error);
    }
  }
}
